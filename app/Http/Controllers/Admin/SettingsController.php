<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Setting; // Make sure to import the Setting model

class SettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Fetch all settings, keyed by their 'setting_key' for easy access in the view
        $settings = Setting::all()->keyBy('setting_key');

        // Define default values for required settings if they are not in the database
        $defaultSettings = [
            'subscription_enabled' => 'false', // Storing as string 'true'/'false' or '1'/'0'
            'pay_per_download_enabled' => 'false',
            'per_download_cost' => '1.00',
            'free_download_enabled' => 'true',
            'google_client_id' => '',
            'google_client_secret' => '',
            'google_redirect_url' => '',
            'razorpay_key_id' => '',
            'razorpay_key_secret' => '',
            'razorpay_webhook_secret' => '',
        ];

        // Ensure all default settings are present in the $settings collection passed to the view
        foreach ($defaultSettings as $key => $value) {
            if (!isset($settings[$key])) {
                // Create a new Setting model instance for defaults not in DB
                // This doesn't save it, just makes it available to the view
                $setting = new Setting();
                $setting->setting_key = $key;
                $setting->setting_value = $value;
                $settings[$key] = $setting;
            }
        }

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Store a newly created or updated resource in storage.
     */
    public function store(Request $request)
    {
        $input = $request->except('_token');

        foreach ($input as $key => $value) {
            // Handle boolean values from checkboxes/toggles explicitly
            // HTML forms don't send unchecked checkbox values, so default to 'false' or 0
            if (in_array($key, ['subscription_enabled', 'pay_per_download_enabled', 'free_download_enabled'])) {
                $value = $request->has($key) ? 'true' : 'false';
            }

            Setting::updateOrCreate(
                ['setting_key' => $key],
                [
                    'setting_value' => $value,
                    'comment' => 'Auto-generated setting'
                ]
            );
        }

        // Ensure default settings exist after store operation, especially if form doesn't submit all fields
        $defaultSettings = [
            'subscription_enabled' => $request->has('subscription_enabled') ? 'true' : 'false',
            'pay_per_download_enabled' => $request->has('pay_per_download_enabled') ? 'true' : 'false',
            'per_download_cost' => $request->input('per_download_cost', '1.00'), // Keep existing or default
            'free_download_enabled' => $request->has('free_download_enabled') ? 'true' : 'true', // Default true if not submitted
            'google_client_id' => $request->input('google_client_id', ''),
            'google_client_secret' => $request->input('google_client_secret', ''),
            'google_redirect_url' => $request->input('google_redirect_url', ''),
            'razorpay_key_id' => $request->input('razorpay_key_id', ''),
            'razorpay_key_secret' => $request->input('razorpay_key_secret', ''),
            'razorpay_webhook_secret' => $request->input('razorpay_webhook_secret', ''),
        ];

        foreach ($defaultSettings as $key => $value) {
             Setting::updateOrCreate(
                ['setting_key' => $key],
                [
                    'setting_value' => $value,
                    'comment' => 'Default application setting'
                ]
            );
        }


        return redirect()->route('admin.settings.index')->with('success', 'Settings updated successfully.');
    }
}
